{"info": {"name": "Scribble User Management API", "description": "Complete API collection for managing users in Scribble admin", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8001", "description": "Base URL for AI Core service"}, {"key": "auth_token", "value": "", "description": "Bearer token for authentication"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"your_password\"\n}"}, "url": {"raw": "{{base_url}}/v1/auth/login", "host": ["{{base_url}}"], "path": ["v1", "auth", "login"]}, "description": "Login to get authentication token"}}]}, {"name": "User Management", "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<EMAIL>\",\n  \"name\": \"New User\",\n  \"email\": \"<EMAIL>\",\n  \"password_hash\": \"hashed_password_here\",\n  \"roles\": [\"User\"]\n}"}, "url": {"raw": "{{base_url}}/v1/user", "host": ["{{base_url}}"], "path": ["v1", "user"]}, "description": "Create a new user in the system"}}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/v1/user-list", "host": ["{{base_url}}"], "path": ["v1", "user-list"]}, "description": "Retrieve all users in the system"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/v1/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["v1", "user", "{{user_id}}"]}, "description": "Retrieve a specific user by ID"}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<EMAIL>\",\n  \"name\": \"Updated User Name\",\n  \"email\": \"<EMAIL>\",\n  \"password_hash\": \"new_hashed_password\",\n  \"roles\": [\"Admin\"]\n}"}, "url": {"raw": "{{base_url}}/v1/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["v1", "user", "{{user_id}}"]}, "description": "Update an existing user"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/v1/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["v1", "user", "{{user_id}}"]}, "description": "Delete a user from the system"}}]}, {"name": "User Registration", "item": [{"name": "Create Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"user_password\",\n  \"state\": \"registered\"\n}"}, "url": {"raw": "{{base_url}}/v1/registration", "host": ["{{base_url}}"], "path": ["v1", "registration"]}, "description": "Create a new user registration"}}]}]}