#!/usr/bin/env python3

# Your provided list of 119 question codes
your_codes = [
    "M0018", "M0010", "M0014", "M0016", "M0020", "M0030", "M0040", "M0050", "M0060", "M0064",
    "M0063", "M0065", "M0066", "A1005", "A1010", "M0069", "M0150", "A1110A", "A1110B", "M0080",
    "M0090", "M0100", "M0102", "M0104", "A1250", "M1000", "M1005", "B0200", "B1000", "B1300",
    "C0100", "C0200", "C0300A", "C0300B", "C0300C", "C0400A", "C0400B", "C0400C", "C0500", "C1310A",
    "C1310B", "C1310C", "C1310D", "M1700", "M1710", "M1720", "D0150A1", "D0150A2", "D0150B1", "D0150B2",
    "D0150C1", "D0150C2", "D0150D1", "D0150D2", "D0150E1", "D0150E2", "D0150F1", "D0150F2", "D0150G1", "D0150G2",
    "D0150H1", "D0150H2", "D0150I1", "D0150I2", "D0160", "D0700", "M1740", "M1745", "M1100", "M2102f",
    "M1800", "M1810", "M1820", "M1830", "M1840", "M1845", "M1850", "M1860", "GG0100", "GG0110",
    "GG0130", "GG0170", "M1600", "M1610", "M1620", "M1630", "M1021", "M1023", "M1028", "M1033",
    "J0510", "J0520", "J0530", "M1400", "M1060", "K0520A", "M1870", "M1306", "M1307", "M1311",
    "M1311A1", "M1311B1", "M1311D1", "M1311D2", "M1311F1", "M1322", "M1324", "M1330", "M1332", "M1334",
    "M1340", "M1342", "N0415", "M2001", "M2003", "M2010", "M2020", "M2030", "O0110"
]

# Codes found in sandeep_questions.json (from grep output)
sandeep_codes = [
    "A1005", "A1010", "A1110.A", "A1110.B", "A1250", "B0200", "B1000", "B1300", "C0100", "C0200",
    "C0300.A", "C0300.B", "C0300.C", "C0400.A", "C0400.B", "C0400.C", "C0500", "C1310.A", "C1310.B", "C1310.C",
    "C1310.D", "D0150.A1", "D0150.A2", "D0150.B1", "D0150.B2", "D0150.C1", "D0150.C2", "D0150.D1", "D0150.D2", "D0150.E1",
    "D0150.E2", "D0150.F1", "D0150.F2", "D0150.G1", "D0150.G2", "D0150.H1", "D0150.H2", "D0150.I1", "D0150.I2", "D0160",
    "D0700", "GG0100.A", "GG0100.B", "GG0100.C", "GG0100.D", "GG0110", "GG0130.A", "GG0130.B", "GG0130.C", "GG0130.E",
    "GG0130.F", "GG0130.G", "GG0130.H", "GG0170.A", "GG0170.B", "GG0170.C", "GG0170.D", "GG0170.E", "GG0170.F", "GG0170.G",
    "GG0170.I", "GG0170.J", "GG0170.K", "GG0170.L", "GG0170.M", "GG0170.N", "GG0170.O", "GG0170.P", "GG0170.Q", "GG0170.R",
    "GG0170.RR1", "GG0170.S", "GG0170.SS1", "J0510", "J0520", "J0530", "M0080", "M0090", "M0100", "M0102.A",
    "M1000.A", "M1005.A", "M1005.B", "M1028", "M1033.A", "M1033.B", "M1060.A", "M1060.B", "M1100", "M1306",
    "M1311.A1", "M1311.B1", "M1311.C1", "M1311.D1", "M1311.E1", "M1311.F1", "M1322", "M1324", "M1330", "M1332",
    "M1334", "M1340", "M1342", "M1400.A", "M1400.B", "M1600", "M1610", "M1620", "M1630", "M1700",
    "M1710", "M1720", "M1740", "M1745", "M1800", "M1810", "M1820", "M1830", "M1840", "M1845",
    "M1850", "M1860", "M1870", "M2001", "M2003", "M2010", "M2020", "M2030", "M2102"
]

def normalize_code(code):
    """Convert between different formats for comparison"""
    # Remove 'f' suffix
    if code.endswith('f'):
        code = code[:-1]
    
    # Convert A1110A to A1110.A
    if len(code) >= 6 and code[-1].isalpha() and code[-2].isdigit():
        return code[:-1] + '.' + code[-1]
    
    # Convert D0150A1 to D0150.A1
    if len(code) >= 7 and code[-2].isalpha() and code[-1].isdigit():
        return code[:-2] + '.' + code[-2:]
    
    # Convert M1311A1 to M1311.A1
    if len(code) >= 7 and code[5].isalpha() and code[6:].isdigit():
        return code[:5] + '.' + code[5:]
    
    # Convert GG0100 to GG0100.* (for partial matching)
    if code.startswith('GG') and len(code) == 6:
        return code + '.*'
    
    return code

def find_matches(your_code, sandeep_codes):
    """Find if your_code matches any sandeep code in various formats"""
    normalized_your = normalize_code(your_code)
    
    # Direct match
    if your_code in sandeep_codes:
        return [your_code]
    
    # Normalized match
    if normalized_your in sandeep_codes:
        return [normalized_your]
    
    # For GG codes, find all sub-components
    if normalized_your.endswith('.*'):
        base_code = normalized_your[:-2]
        matches = [code for code in sandeep_codes if code.startswith(base_code + '.')]
        if matches:
            return matches
    
    # Check if any sandeep code normalizes to your code
    for sandeep_code in sandeep_codes:
        if normalize_code(sandeep_code) == your_code:
            return [sandeep_code]
    
    return []

# Find codes in sandeep but not in your list
missing_from_your_list = []
matched_codes = set()

for sandeep_code in sandeep_codes:
    found = False
    for your_code in your_codes:
        matches = find_matches(your_code, [sandeep_code])
        if matches:
            found = True
            matched_codes.add(sandeep_code)
            break
    if not found:
        missing_from_your_list.append(sandeep_code)

# Find codes in your list but not in sandeep
missing_from_sandeep = []
for your_code in your_codes:
    matches = find_matches(your_code, sandeep_codes)
    if not matches:
        missing_from_sandeep.append(your_code)

print("=== COMPARISON: Your List vs Sandeep Questions ===")
print(f"Your list: {len(your_codes)} codes")
print(f"Sandeep questions: {len(sandeep_codes)} codes")
print(f"Matched codes: {len(matched_codes)}")
print(f"Missing from your list: {len(missing_from_your_list)} codes")
print(f"Missing from sandeep: {len(missing_from_sandeep)} codes")

print(f"\n=== CODES IN SANDEEP BUT NOT IN YOUR LIST ({len(missing_from_your_list)}) ===")
for code in sorted(missing_from_your_list):
    print(code)

print(f"\n=== CODES IN YOUR LIST BUT NOT IN SANDEEP ({len(missing_from_sandeep)}) ===")
for code in sorted(missing_from_sandeep):
    print(code)

# Analyze patterns
print(f"\n=== PATTERN ANALYSIS ===")

# GG codes analysis
gg_in_sandeep = [code for code in missing_from_your_list if code.startswith('GG')]
gg_in_your_list = [code for code in missing_from_sandeep if code.startswith('GG')]

print(f"GG codes in sandeep but not in your list: {len(gg_in_sandeep)}")
print(f"GG codes in your list but not in sandeep: {len(gg_in_your_list)}")

# M codes analysis
m_in_sandeep = [code for code in missing_from_your_list if code.startswith('M')]
m_in_your_list = [code for code in missing_from_sandeep if code.startswith('M')]

print(f"M codes in sandeep but not in your list: {len(m_in_sandeep)}")
print(f"M codes in your list but not in sandeep: {len(m_in_your_list)}")

# Show some examples
if gg_in_sandeep:
    print(f"\nExample GG codes in sandeep: {gg_in_sandeep[:5]}")
if m_in_your_list:
    print(f"Example M codes missing from sandeep: {m_in_your_list[:10]}")
